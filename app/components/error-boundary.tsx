import React, { Component } from "react";
import { AppError, handleError, ErrorSeverity, ErrorType } from "../lib/errors";

type ReactNode = React.ReactNode;

interface ErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
  retryCount: number;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: AppError, retry: () => void) => ReactNode;
  onError?: (error: AppError) => void;
  maxRetries?: number;
  level?: "page" | "section" | "component";
}

export class ErrorBoundary extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const appError = handleError(error, { action: "component_render" });
    return {
      hasError: true,
      error: appError,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const appError = handleError(error, {
      action: "component_render",
      additionalData: {
        componentStack: errorInfo.componentStack,
        errorBoundary: this.props.level || "unknown",
      },
    });

    this.props.onError?.(appError);
  }

  componentDidUpdate(
    prevProps: ErrorBoundaryProps,
    prevState: ErrorBoundaryState
  ) {
    // Reset error state when children change (e.g., route change)
    if (prevProps.children !== this.props.children && this.state.hasError) {
      this.setState({
        hasError: false,
        error: null,
        retryCount: 0,
      });
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  retry = () => {
    const { maxRetries = 3 } = this.props;
    const { retryCount, error } = this.state;

    if (retryCount >= maxRetries) {
      return;
    }

    if (error && !error.isRetryable) {
      return;
    }

    this.setState((prevState) => ({
      hasError: false,
      error: null,
      retryCount: prevState.retryCount + 1,
    }));
  };

  autoRetry = () => {
    const { error } = this.state;
    if (error && error.isRetryable) {
      // Auto-retry after a delay for retryable errors
      this.retryTimeoutId = setTimeout(() => {
        this.retry();
      }, Math.min(1000 * Math.pow(2, this.state.retryCount), 10000)); // Exponential backoff, max 10s
    }
  };

  render() {
    if (this.state.hasError && this.state.error) {
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.retry);
      }

      return (
        <DefaultErrorFallback error={this.state.error} onRetry={this.retry} />
      );
    }

    return this.props.children;
  }
}

interface DefaultErrorFallbackProps {
  error: AppError;
  onRetry: () => void;
}

function DefaultErrorFallback({ error, onRetry }: DefaultErrorFallbackProps) {
  const canRetry = error.isRetryable;
  const isMinorError = error.severity === ErrorSeverity.LOW;

  if (isMinorError) {
    return (
      <div className="error-boundary-minor">
        <p className="error-message">{error.userMessage}</p>
        {canRetry && (
          <button onClick={onRetry} className="retry-button">
            Try Again
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="error-boundary-major">
      <div className="error-content">
        <h2>Something went wrong</h2>
        <p className="error-message">{error.userMessage}</p>

        {error.type === ErrorType.NETWORK && (
          <div className="error-details">
            <p>Please check your internet connection and try again.</p>
          </div>
        )}

        <div className="error-actions">
          {canRetry && (
            <button onClick={onRetry} className="retry-button primary">
              Try Again
            </button>
          )}
          <button
            onClick={() => window.location.reload()}
            className="retry-button secondary"
          >
            Reload Page
          </button>
        </div>

        {process.env.NODE_ENV === "development" && (
          <details className="error-debug">
            <summary>Debug Information</summary>
            <pre>{JSON.stringify(error, null, 2)}</pre>
          </details>
        )}
      </div>
    </div>
  );
}

// Specialized error boundaries for different contexts
export function PageErrorBoundary({
  children,
  onError,
}: {
  children: ReactNode;
  onError?: (error: AppError) => void;
}) {
  return (
    <ErrorBoundary
      level="page"
      maxRetries={2}
      onError={onError}
      fallback={(error, retry) => (
        <div className="page-error">
          <h1>Page Error</h1>
          <DefaultErrorFallback error={error} onRetry={retry} />
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  );
}

export function SectionErrorBoundary({
  children,
  title,
}: {
  children: ReactNode;
  title?: string;
}) {
  return (
    <ErrorBoundary
      level="section"
      maxRetries={3}
      fallback={(error, retry) => (
        <div className="section-error">
          {title && <h3>{title} - Error</h3>}
          <DefaultErrorFallback error={error} onRetry={retry} />
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  );
}

export function ComponentErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary
      level="component"
      maxRetries={1}
      fallback={(error, retry) => (
        <div className="component-error">
          <span className="error-icon">⚠️</span>
          <span>{error.userMessage}</span>
          {error.isRetryable && (
            <button onClick={retry} className="retry-link">
              Retry
            </button>
          )}
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  );
}
