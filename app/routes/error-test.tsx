import React, { useState } from 'react';
import { SectionErrorBoundary, ComponentErrorBoundary } from '../components/error-boundary';
import { useToast, useErrorToast } from '../components/toast';
import { NetworkError, ValidationError, AppError } from '../lib/errors';

export default function ErrorTest() {
  const [shouldThrow, setShouldThrow] = useState(false);
  const { showSuccess, showWarning, showInfo } = useToast();
  const showErrorToast = useErrorToast();

  const testNetworkError = () => {
    const error = new NetworkError('Failed to connect to server');
    showErrorToast(error);
  };

  const testValidationError = () => {
    const error = new ValidationError('Email format is invalid', 'email', 'format');
    showErrorToast(error);
  };

  const testComponentError = () => {
    setShouldThrow(true);
  };

  const testToastNotifications = () => {
    showSuccess('Operation completed successfully!');
    setTimeout(() => showWarning('This is a warning message'), 1000);
    setTimeout(() => showInfo('Here is some information'), 2000);
  };

  if (shouldThrow) {
    throw new Error('This is a test component error');
  }

  return (
    <div style={{ padding: '2rem' }}>
      <h1>Error Handling Test Page</h1>
      <p>This page demonstrates the various error handling features.</p>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Toast Notifications</h2>
        <button onClick={testToastNotifications} style={{ marginRight: '1rem' }}>
          Test Success/Warning/Info Toasts
        </button>
        <button onClick={testNetworkError} style={{ marginRight: '1rem' }}>
          Test Network Error Toast
        </button>
        <button onClick={testValidationError}>
          Test Validation Error Toast
        </button>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Error Boundaries</h2>
        <button onClick={testComponentError} style={{ marginRight: '1rem' }}>
          Test Component Error Boundary
        </button>
        <button onClick={() => setShouldThrow(false)}>
          Reset Component
        </button>
      </div>

      <SectionErrorBoundary title="Test Section">
        <div style={{ padding: '1rem', border: '1px solid #ddd', borderRadius: '4px' }}>
          <h3>Section with Error Boundary</h3>
          <p>This section is protected by an error boundary.</p>
          
          <ComponentErrorBoundary>
            <TestComponent />
          </ComponentErrorBoundary>
        </div>
      </SectionErrorBoundary>

      <div style={{ marginTop: '2rem' }}>
        <h2>Form Validation Test</h2>
        <TestForm />
      </div>
    </div>
  );
}

function TestComponent() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new AppError('Test component error', 'CLIENT', 'MEDIUM');
  }

  return (
    <div style={{ padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
      <p>This component is wrapped in a ComponentErrorBoundary.</p>
      <button onClick={() => setShouldError(true)}>
        Trigger Component Error
      </button>
    </div>
  );
}

function TestForm() {
  const [formData, setFormData] = useState({ email: '', name: '' });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const showErrorToast = useErrorToast();

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      const firstError = Object.values(errors)[0];
      showErrorToast(new ValidationError(firstError));
      return;
    }
    
    // Simulate form submission
    console.log('Form submitted:', formData);
  };

  return (
    <form onSubmit={handleSubmit} style={{ maxWidth: '400px' }}>
      <div style={{ marginBottom: '1rem' }}>
        <label htmlFor="name">Name:</label>
        <input
          id="name"
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          style={{ 
            width: '100%', 
            padding: '0.5rem', 
            border: errors.name ? '1px solid red' : '1px solid #ddd',
            borderRadius: '4px'
          }}
        />
        {errors.name && <div style={{ color: 'red', fontSize: '0.9rem' }}>{errors.name}</div>}
      </div>
      
      <div style={{ marginBottom: '1rem' }}>
        <label htmlFor="email">Email:</label>
        <input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          style={{ 
            width: '100%', 
            padding: '0.5rem', 
            border: errors.email ? '1px solid red' : '1px solid #ddd',
            borderRadius: '4px'
          }}
        />
        {errors.email && <div style={{ color: 'red', fontSize: '0.9rem' }}>{errors.email}</div>}
      </div>
      
      <button type="submit" style={{ padding: '0.5rem 1rem', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '4px' }}>
        Submit
      </button>
    </form>
  );
}
