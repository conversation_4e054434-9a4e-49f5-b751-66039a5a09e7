import React from "react";
import { ValidationError, handleError } from "./errors";

export interface ValidationRule {
  validate: (value: any) => boolean;
  message: string;
}

export interface FieldValidation {
  rules: ValidationRule[];
  required?: boolean;
}

export interface ValidationSchema {
  [fieldName: string]: FieldValidation;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string[]>;
  firstError?: string;
}

// Common validation rules
export const validationRules = {
  required: (message = "This field is required"): ValidationRule => ({
    validate: (value: any) => {
      if (typeof value === "string") return value.trim().length > 0;
      return value != null && value !== "";
    },
    message,
  }),

  minLength: (min: number, message?: string): ValidationRule => ({
    validate: (value: string) => !value || value.length >= min,
    message: message || `Must be at least ${min} characters long`,
  }),

  maxLength: (max: number, message?: string): ValidationRule => ({
    validate: (value: string) => !value || value.length <= max,
    message: message || `Must be no more than ${max} characters long`,
  }),

  email: (message = "Please enter a valid email address"): ValidationRule => ({
    validate: (value: string) => {
      if (!value) return true;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value);
    },
    message,
  }),

  url: (message = "Please enter a valid URL"): ValidationRule => ({
    validate: (value: string) => {
      if (!value) return true;
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    },
    message,
  }),

  pattern: (regex: RegExp, message: string): ValidationRule => ({
    validate: (value: string) => !value || regex.test(value),
    message,
  }),

  custom: (
    validator: (value: any) => boolean,
    message: string
  ): ValidationRule => ({
    validate: validator,
    message,
  }),
};

// Validation function
export function validateField(
  value: any,
  validation: FieldValidation
): string[] {
  const errors: string[] = [];

  // Check required first
  if (validation.required && !validationRules.required().validate(value)) {
    errors.push("This field is required");
    return errors; // Don't run other validations if required fails
  }

  // Run other validation rules
  for (const rule of validation.rules) {
    if (!rule.validate(value)) {
      errors.push(rule.message);
    }
  }

  return errors;
}

export function validateForm(
  data: Record<string, any>,
  schema: ValidationSchema
): ValidationResult {
  const errors: Record<string, string[]> = {};
  let isValid = true;
  let firstError: string | undefined;

  for (const [fieldName, validation] of Object.entries(schema)) {
    const fieldValue = data[fieldName];
    const fieldErrors = validateField(fieldValue, validation);

    if (fieldErrors.length > 0) {
      errors[fieldName] = fieldErrors;
      isValid = false;
      if (!firstError) {
        firstError = fieldErrors[0];
      }
    }
  }

  return { isValid, errors, firstError };
}

// Contact form validation schema
export const contactValidationSchema: ValidationSchema = {
  first: {
    required: true,
    rules: [
      validationRules.minLength(1, "First name is required"),
      validationRules.maxLength(
        50,
        "First name must be less than 50 characters"
      ),
    ],
  },
  last: {
    required: true,
    rules: [
      validationRules.minLength(1, "Last name is required"),
      validationRules.maxLength(
        50,
        "Last name must be less than 50 characters"
      ),
    ],
  },
  twitter: {
    required: false,
    rules: [
      validationRules.pattern(
        /^@?[a-zA-Z0-9_]+$/,
        "Twitter handle can only contain letters, numbers, and underscores"
      ),
      validationRules.maxLength(
        15,
        "Twitter handle must be less than 15 characters"
      ),
    ],
  },
  avatar: {
    required: false,
    rules: [validationRules.url("Please enter a valid URL for the avatar")],
  },
  notes: {
    required: false,
    rules: [
      validationRules.maxLength(500, "Notes must be less than 500 characters"),
    ],
  },
};

// Hook for form validation
export function useFormValidation(schema: ValidationSchema) {
  const [errors, setErrors] = React.useState<Record<string, string[]>>({});
  const [touched, setTouched] = React.useState<Record<string, boolean>>({});

  const validateFieldHook = React.useCallback(
    (fieldName: string, value: any) => {
      const validation = schema[fieldName];
      if (!validation) return [];

      const fieldErrors = validateField(value, validation);
      setErrors((prev) => ({
        ...prev,
        [fieldName]: fieldErrors,
      }));

      return fieldErrors;
    },
    [schema]
  );

  const validateFormHook = React.useCallback(
    (data: Record<string, any>) => {
      const result = validateForm(data, schema);
      setErrors(result.errors);
      return result;
    },
    [schema]
  );

  const markFieldTouched = React.useCallback((fieldName: string) => {
    setTouched((prev) => ({
      ...prev,
      [fieldName]: true,
    }));
  }, []);

  const clearErrors = React.useCallback(() => {
    setErrors({});
    setTouched({});
  }, []);

  const getFieldError = React.useCallback(
    (fieldName: string) => {
      if (!touched[fieldName]) return undefined;
      return errors[fieldName]?.[0];
    },
    [errors, touched]
  );

  const hasFieldError = React.useCallback(
    (fieldName: string) => {
      return touched[fieldName] && errors[fieldName]?.length > 0;
    },
    [errors, touched]
  );

  return {
    errors,
    touched,
    validateField: validateFieldHook,
    validateForm: validateFormHook,
    markFieldTouched,
    clearErrors,
    getFieldError,
    hasFieldError,
  };
}

// Utility to create validation errors for server-side validation
export function createValidationError(
  field: string,
  message: string
): ValidationError {
  return new ValidationError(message, field, "server_validation");
}

// Utility to handle form submission with validation
export async function handleFormSubmission<T>(
  formData: FormData,
  schema: ValidationSchema,
  submitFn: (data: Record<string, any>) => Promise<T>
): Promise<T> {
  const data = Object.fromEntries(formData);
  const validation = validateForm(data, schema);

  if (!validation.isValid) {
    const firstField = Object.keys(validation.errors)[0];
    const firstError = validation.errors[firstField][0];
    throw new ValidationError(firstError, firstField, "client_validation");
  }

  try {
    return await submitFn(data);
  } catch (error) {
    throw handleError(error, { action: "form_submission" });
  }
}
